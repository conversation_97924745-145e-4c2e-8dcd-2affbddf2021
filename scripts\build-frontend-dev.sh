#!/bin/bash
# AlgoTrader Frontend Development Container Build Script
# Production-level build and testing script for multi-stage Docker container
# Last Updated: 2025-01-27

set -e

# Enhanced logging functions
log() {
    echo "[$(date "+%Y-%m-%d %H:%M:%S")] [INFO] $1"
}

log_warn() {
    echo "[$(date "+%Y-%m-%d %H:%M:%S")] [WARN] $1"
}

log_error() {
    echo "[$(date "+%Y-%m-%d %H:%M:%S")] [ERROR] $1"
}

log_success() {
    echo "[$(date "+%Y-%m-%d %H:%M:%S")] [SUCCESS] $1"
}

# Configuration
CONTAINER_NAME="algotrader-frontend-dev"
IMAGE_NAME="algotrader-frontend-dev"
DOCKERFILE_PATH="./frontend/Dockerfile.dev"
BUILD_CONTEXT="./frontend"
COMPOSE_SERVICE="frontend-dev"

# Build options
FORCE_REBUILD=${1:-false}
SKIP_TESTS=${2:-false}
VERBOSE=${3:-false}

# Function to check if container exists
container_exists() {
    docker ps -a --format "table {{.Names}}" | grep -q "^${CONTAINER_NAME}$"
}

# Function to check if container is running
container_running() {
    docker ps --format "table {{.Names}}" | grep -q "^${CONTAINER_NAME}$"
}

# Function to stop and remove existing container
cleanup_existing() {
    if container_running; then
        log "Stopping existing container: $CONTAINER_NAME"
        docker stop "$CONTAINER_NAME" >/dev/null 2>&1 || true
    fi
    
    if container_exists; then
        log "Removing existing container: $CONTAINER_NAME"
        docker rm "$CONTAINER_NAME" >/dev/null 2>&1 || true
    fi
}

# Function to build the Docker image
build_image() {
    log "================================================"
    log "🏗️  Building AlgoTrader Frontend Development Container"
    log "================================================"
    log "Dockerfile: $DOCKERFILE_PATH"
    log "Build Context: $BUILD_CONTEXT"
    log "Target Stage: runtime"
    log "Force Rebuild: $FORCE_REBUILD"
    log "================================================"
    
    # Build arguments
    local build_args=""
    if [ "$VERBOSE" = "true" ]; then
        build_args="--progress=plain --no-cache"
    else
        build_args="--progress=auto"
    fi
    
    if [ "$FORCE_REBUILD" = "true" ]; then
        build_args="$build_args --no-cache"
        log "Force rebuild enabled - clearing Docker cache"
    fi
    
    # Build the image
    log "Starting Docker build process..."
    if docker build \
        $build_args \
        --target runtime \
        --tag "$IMAGE_NAME:latest" \
        --tag "$IMAGE_NAME:dev" \
        --file "$DOCKERFILE_PATH" \
        "$BUILD_CONTEXT"; then
        log_success "Docker image built successfully: $IMAGE_NAME:latest"
    else
        log_error "Docker build failed"
        return 1
    fi
}

# Function to test the built image
test_image() {
    if [ "$SKIP_TESTS" = "true" ]; then
        log "Skipping image tests as requested"
        return 0
    fi
    
    log "================================================"
    log "🧪 Testing Docker Image"
    log "================================================"
    
    # Test 1: Basic container startup
    log "Test 1: Basic container startup test..."
    if docker run --rm --name "${CONTAINER_NAME}-test" \
        -e NODE_ENV=development \
        "$IMAGE_NAME:latest" \
        /usr/local/bin/dev-tools.sh health; then
        log_success "✓ Basic startup test passed"
    else
        log_error "✗ Basic startup test failed"
        return 1
    fi
    
    # Test 2: Development tools availability
    log "Test 2: Development tools availability test..."
    if docker run --rm --name "${CONTAINER_NAME}-tools-test" \
        "$IMAGE_NAME:latest" \
        /usr/local/bin/dev-tools.sh help >/dev/null 2>&1; then
        log_success "✓ Development tools test passed"
    else
        log_error "✗ Development tools test failed"
        return 1
    fi
    
    # Test 3: Health check script
    log "Test 3: Health check script test..."
    if docker run --rm --name "${CONTAINER_NAME}-health-test" \
        "$IMAGE_NAME:latest" \
        /usr/local/bin/healthcheck.sh; then
        log_success "✓ Health check test passed"
    else
        log_error "✗ Health check test failed"
        return 1
    fi
    
    log_success "All image tests passed successfully"
}

# Function to start the container using docker-compose
start_container() {
    log "================================================"
    log "🚀 Starting Frontend Development Container"
    log "================================================"
    
    # Ensure we're in the right directory
    if [ ! -f "docker-compose.yml" ]; then
        log_error "docker-compose.yml not found. Please run this script from the project root."
        return 1
    fi
    
    # Start the service
    log "Starting $COMPOSE_SERVICE service..."
    if docker-compose up -d "$COMPOSE_SERVICE"; then
        log_success "Container started successfully"
        
        # Wait for container to be ready
        log "Waiting for container to be ready..."
        local timeout=60
        local count=0
        
        while [ $count -lt $timeout ]; do
            if docker-compose exec -T "$COMPOSE_SERVICE" /usr/local/bin/healthcheck.sh >/dev/null 2>&1; then
                log_success "Container is ready and healthy"
                break
            fi
            sleep 2
            count=$((count + 2))
            log "Waiting... ($count/$timeout seconds)"
        done
        
        if [ $count -ge $timeout ]; then
            log_error "Container failed to become ready within $timeout seconds"
            return 1
        fi
        
        # Show container information
        log "================================================"
        log "📋 Container Information"
        log "================================================"
        docker-compose exec -T "$COMPOSE_SERVICE" /usr/local/bin/dev-tools.sh info
        log "================================================"
        log "🌐 Access Information"
        log "================================================"
        log "Frontend Development Server: http://localhost:3000"
        log "Debug Port: http://localhost:9229"
        log "Container Name: $CONTAINER_NAME"
        log "================================================"
        
    else
        log_error "Failed to start container"
        return 1
    fi
}

# Function to show usage
show_usage() {
    echo "Usage: $0 [FORCE_REBUILD] [SKIP_TESTS] [VERBOSE]"
    echo ""
    echo "Arguments:"
    echo "  FORCE_REBUILD  - Force rebuild without cache (true/false, default: false)"
    echo "  SKIP_TESTS     - Skip image testing (true/false, default: false)"
    echo "  VERBOSE        - Enable verbose output (true/false, default: false)"
    echo ""
    echo "Examples:"
    echo "  $0                    # Normal build with tests"
    echo "  $0 true               # Force rebuild with tests"
    echo "  $0 false true         # Normal build, skip tests"
    echo "  $0 true false true    # Force rebuild with tests and verbose output"
}

# Main execution
main() {
    log "================================================"
    log "🐳 AlgoTrader Frontend Development Container Builder"
    log "================================================"
    log "Force Rebuild: $FORCE_REBUILD"
    log "Skip Tests: $SKIP_TESTS"
    log "Verbose: $VERBOSE"
    log "================================================"
    
    # Check if help is requested
    if [ "$1" = "help" ] || [ "$1" = "--help" ] || [ "$1" = "-h" ]; then
        show_usage
        exit 0
    fi
    
    # Check Docker availability
    if ! command -v docker >/dev/null 2>&1; then
        log_error "Docker is not installed or not in PATH"
        exit 1
    fi
    
    if ! command -v docker-compose >/dev/null 2>&1; then
        log_error "Docker Compose is not installed or not in PATH"
        exit 1
    fi
    
    # Check if Docker is running
    if ! docker info >/dev/null 2>&1; then
        log_error "Docker daemon is not running"
        exit 1
    fi
    
    # Execute build steps
    log "Step 1/4: Cleaning up existing containers..."
    cleanup_existing
    
    log "Step 2/4: Building Docker image..."
    if ! build_image; then
        log_error "Build failed"
        exit 1
    fi
    
    log "Step 3/4: Testing Docker image..."
    if ! test_image; then
        log_error "Image testing failed"
        exit 1
    fi
    
    log "Step 4/4: Starting container..."
    if ! start_container; then
        log_error "Container startup failed"
        exit 1
    fi
    
    log_success "================================================"
    log_success "🎉 Frontend Development Container Ready!"
    log_success "================================================"
    log_success "The AlgoTrader frontend development environment is now running."
    log_success "You can start developing with hot-reloading enabled."
    log_success "================================================"
}

# Execute main function with all arguments
main "$@"
