﻿# AlgoTrader Frontend Development Container - Production Level Multi-Stage Build
# Comprehensive development environment with optimized multi-stage build approach
# Implements hot-reloading, isolated dependencies, and code management on local machine
# Last Updated: 2025-01-27

# ================================================================================================
# Stage 1: Base Stage - Common foundation with all dependencies
# ================================================================================================
FROM node:22.15.0-alpine AS base

# Install comprehensive system dependencies for all stages
RUN apk add --no-cache \
    libc6-compat \
    python3 \
    py3-pip \
    make \
    g++ \
    git \
    curl \
    wget \
    ca-certificates \
    bash \
    dumb-init \
    openssh-client \
    rsync \
    su-exec \
    tini \
    shadow \
    procps \
    util-linux \
    findutils \
    && rm -rf /var/cache/apk/*

# Set working directory
WORKDIR /app

# Create non-root user for security (production-level approach)
RUN addgroup -g 1001 -S nodejs && \
    adduser -S nextjs -u 1001 -G nodejs

# Create essential directories with proper permissions
RUN mkdir -p /app/.next /app/node_modules /app/coverage /app/test-results /app/logs && \
    chown -R nextjs:nodejs /app

# ================================================================================================
# Stage 2: Dependencies Stage - Isolated dependency management
# ================================================================================================
FROM base AS dependencies

# Copy package files for dependency installation (production-level caching)
COPY package*.json ./
COPY tsconfig.json ./

# Install all dependencies with comprehensive error handling and optimization
RUN npm ci --legacy-peer-deps --no-audit --no-fund --verbose && \
    npm cache clean --force

# Install additional development tools and utilities for comprehensive development
RUN npm install --no-save --legacy-peer-deps \
    glob \
    cross-env \
    concurrently \
    nodemon \
    @types/node \
    typescript \
    webpack-dev-server \
    @next/bundle-analyzer

# Verify installation integrity and create dependency manifest
RUN npm ls --depth=0 > /app/dependency-manifest.txt || echo "Dependency verification completed with warnings" && \
    echo "Dependencies installed at: $(date)" >> /app/dependency-manifest.txt

# ================================================================================================
# Stage 3: Development Stage - Hot-reloading and development tools
# ================================================================================================
FROM base AS development

# Copy dependencies from isolated dependencies stage
COPY --from=dependencies --chown=nextjs:nodejs /app/node_modules ./node_modules
COPY --from=dependencies --chown=nextjs:nodejs /app/package*.json ./
COPY --from=dependencies --chown=nextjs:nodejs /app/dependency-manifest.txt ./

# Copy configuration files for optimal layer caching (production approach)
COPY --chown=nextjs:nodejs next.config.js ./
COPY --chown=nextjs:nodejs tailwind.config.js ./
COPY --chown=nextjs:nodejs postcss.config.js ./
COPY --chown=nextjs:nodejs eslint.config.js ./
COPY --chown=nextjs:nodejs jest.config.js ./
COPY --chown=nextjs:nodejs tsconfig.json ./

# ================================================================================================
# Stage 4: Production Build Stage (for testing and validation)
# ================================================================================================
FROM development AS builder

# Set production environment for build
ENV NODE_ENV=production

# Build the application for validation
RUN npm run build

# ================================================================================================
# Stage 5: Development Runtime (default stage)
# ================================================================================================
FROM development AS runtime

# Create comprehensive development scripts and health checks
RUN echo '#!/bin/bash\n\
# Comprehensive health check for development environment\n\
set -e\n\
\n\
# Function to check service health\n\
check_service() {\n\
    local url="$1"\n\
    local timeout="${2:-10}"\n\
    \n\
    if curl -f -s --max-time "$timeout" "$url" >/dev/null 2>&1; then\n\
        return 0\n\
    else\n\
        return 1\n\
    fi\n\
}\n\
\n\
# Check Next.js development server\n\
if check_service "http://localhost:${PORT:-3000}" 15; then\n\
    echo "✓ Next.js development server is healthy"\n\
    exit 0\n\
elif check_service "http://localhost:${PORT:-3000}/api/health" 10; then\n\
    echo "✓ API health endpoint is responding"\n\
    exit 0\n\
else\n\
    echo "✗ Health check failed - service not responding"\n\
    echo "Checking process status..."\n\
    ps aux | grep -E "(next|node)" | grep -v grep || echo "No Next.js processes found"\n\
    exit 1\n\
fi' > /usr/local/bin/healthcheck.sh && \
    chmod +x /usr/local/bin/healthcheck.sh

# Create enhanced development startup script with comprehensive functionality
RUN echo '#!/bin/bash\n\
# Production-level development startup script for AlgoTrader Frontend\n\
set -e\n\
\n\
# Enhanced logging functions\n\
log() {\n\
    echo "[$(date "+%Y-%m-%d %H:%M:%S")] [INFO] $1"\n\
}\n\
\n\
log_warn() {\n\
    echo "[$(date "+%Y-%m-%d %H:%M:%S")] [WARN] $1"\n\
}\n\
\n\
log_error() {\n\
    echo "[$(date "+%Y-%m-%d %H:%M:%S")] [ERROR] $1"\n\
}\n\
\n\
log_success() {\n\
    echo "[$(date "+%Y-%m-%d %H:%M:%S")] [SUCCESS] $1"\n\
}\n\
\n\
# System information logging\n\
log_system_info() {\n\
    log "=== AlgoTrader Frontend Development Environment ==="\n\
    log "Node.js version: $(node --version)"\n\
    log "NPM version: $(npm --version)"\n\
    log "Environment: ${NODE_ENV:-development}"\n\
    log "Port: ${PORT:-3000}"\n\
    log "User: $(whoami)"\n\
    log "Working Directory: $(pwd)"\n\
    log "Memory limit: ${NODE_OPTIONS:-default}"\n\
    log "Hot-reloading: ${FAST_REFRESH:-true}"\n\
    log "File watching: ${WATCHPACK_POLLING:-true}"\n\
    log "================================================"\n\
}\n\
\n\
# Ensure proper permissions and user context\n\
ensure_proper_user() {\n\
    if [ "$(whoami)" = "root" ]; then\n\
        log_warn "Running as root. Switching to nextjs user for security..."\n\
        exec su-exec nextjs "$0" "$@"\n\
    fi\n\
}\n\
\n\
# Enhanced dependency management with volume detection\n\
check_dependencies() {\n\
    local force_install="${1:-false}"\n\
    \n\
    if [ "$force_install" = "true" ] || [ ! -d "node_modules" ] || [ ! -f "node_modules/.package-lock.json" ]; then\n\
        log "Installing/updating dependencies..."\n\
        \n\
        # Detect if node_modules is a mounted volume\n\
        local is_volume=false\n\
        if mountpoint -q node_modules 2>/dev/null || grep -q "/app/node_modules" /proc/mounts 2>/dev/null; then\n\
            is_volume=true\n\
            log "Detected node_modules as mounted volume"\n\
        fi\n\
        \n\
        if [ "$is_volume" = "true" ]; then\n\
            if [ ! -f "node_modules/.package-lock.json" ] || [ ! -d "node_modules/.bin" ] || [ "$force_install" = "true" ]; then\n\
                log "Installing dependencies in mounted volume..."\n\
                npm install --legacy-peer-deps --no-audit --no-fund --verbose\n\
            else\n\
                log "Dependencies already installed in mounted volume"\n\
            fi\n\
        else\n\
            log "Installing dependencies in container..."\n\
            npm install --legacy-peer-deps --no-audit --no-fund --verbose\n\
        fi\n\
        \n\
        # Verify installation\n\
        if [ -d "node_modules" ] && [ -f "node_modules/.package-lock.json" ]; then\n\
            log_success "Dependencies verification completed"\n\
        else\n\
            log_error "Dependencies installation failed"\n\
            return 1\n\
        fi\n\
    else\n\
        log "Dependencies already installed and verified"\n\
    fi\n\
}\n\
\n\
# Enhanced symlink setup with error handling\n\
setup_symlinks() {\n\
    if [ -f "create-symlinks.js" ]; then\n\
        log "Creating symbolic links for UI components..."\n\
        if node create-symlinks.js; then\n\
            log_success "Symbolic links created successfully"\n\
        else\n\
            log_warn "Symlink creation failed, continuing without symlinks..."\n\
        fi\n\
    else\n\
        log "No symlink configuration found, skipping..."\n\
    fi\n\
}\n\
\n\
# Enhanced configuration verification\n\
verify_config() {\n\
    local config_valid=true\n\
    \n\
    # Check essential files\n\
    if [ -f "next.config.js" ]; then\n\
        log_success "Next.js configuration found"\n\
    else\n\
        log_warn "next.config.js not found - using defaults"\n\
    fi\n\
    \n\
    if [ -f "package.json" ]; then\n\
        log_success "Package.json found"\n\
        # Validate package.json syntax\n\
        if node -e "JSON.parse(require('"'"'fs'"'"').readFileSync('"'"'package.json'"'"', '"'"'utf8'"'"'))" 2>/dev/null; then\n\
            log_success "Package.json syntax is valid"\n\
        else\n\
            log_error "Package.json has invalid syntax"\n\
            config_valid=false\n\
        fi\n\
    else\n\
        log_error "package.json not found"\n\
        config_valid=false\n\
    fi\n\
    \n\
    if [ -f "tsconfig.json" ]; then\n\
        log_success "TypeScript configuration found"\n\
    else\n\
        log_warn "tsconfig.json not found - TypeScript features may be limited"\n\
    fi\n\
    \n\
    if [ "$config_valid" = "false" ]; then\n\
        log_error "Critical configuration issues detected"\n\
        return 1\n\
    fi\n\
    \n\
    return 0\n\
}\n\
\n\
# Enhanced cache clearing with comprehensive volume detection\n\
clear_cache() {\n\
    log "Checking Next.js cache directory..."\n\
    \n\
    if [ -d ".next" ]; then\n\
        # Multiple methods to detect if .next is a mounted volume\n\
        local is_mounted=false\n\
        \n\
        # Method 1: Check if it'"'"'s a mountpoint\n\
        if mountpoint -q .next 2>/dev/null; then\n\
            is_mounted=true\n\
            log "Detected .next as mountpoint"\n\
        fi\n\
        \n\
        # Method 2: Check /proc/mounts for volume mounts\n\
        if grep -q "/app/.next" /proc/mounts 2>/dev/null; then\n\
            is_mounted=true\n\
            log "Detected .next in /proc/mounts"\n\
        fi\n\
        \n\
        # Method 3: Check if directory has docker volume characteristics\n\
        if [ -z "$(ls -A .next 2>/dev/null)" ] && [ "$(stat -c %d .next 2>/dev/null)" != "$(stat -c %d . 2>/dev/null)" ]; then\n\
            is_mounted=true\n\
            log "Detected .next as volume by device ID"\n\
        fi\n\
        \n\
        if [ "$is_mounted" = "true" ]; then\n\
            log_success "Detected .next as mounted volume - clearing contents safely..."\n\
            # Clear contents of mounted volume without removing the mount point\n\
            if [ -n "$(ls -A .next 2>/dev/null)" ]; then\n\
                log "Removing existing cache files from mounted volume..."\n\
                find .next -mindepth 1 -maxdepth 1 -exec rm -rf {} + 2>/dev/null || {\n\
                    log_warn "Some cache files could not be removed (normal for mounted volumes)"\n\
                }\n\
            else\n\
                log_success "Mounted .next volume is already clean"\n\
            fi\n\
        else\n\
            log_success ".next is a regular directory - removing completely..."\n\
            rm -rf .next 2>/dev/null || {\n\
                log_warn "Could not remove .next directory completely"\n\
            }\n\
        fi\n\
    else\n\
        log_success "No existing .next cache found - starting fresh"\n\
    fi\n\
    \n\
    # Ensure .next directory exists for Next.js\n\
    if [ ! -d ".next" ]; then\n\
        log "Creating .next directory for Next.js..."\n\
        mkdir -p .next 2>/dev/null || true\n\
    fi\n\
}\n\
\n\
# Enhanced signal handlers for graceful shutdown\n\
cleanup() {\n\
    log_warn "Received shutdown signal, stopping development server..."\n\
    if [ ! -z "$PID" ]; then\n\
        log "Sending TERM signal to process $PID..."\n\
        kill -TERM $PID 2>/dev/null || true\n\
        \n\
        # Wait for graceful shutdown with timeout\n\
        local timeout=10\n\
        local count=0\n\
        while kill -0 $PID 2>/dev/null && [ $count -lt $timeout ]; do\n\
            sleep 1\n\
            count=$((count + 1))\n\
        done\n\
        \n\
        # Force kill if still running\n\
        if kill -0 $PID 2>/dev/null; then\n\
            log_warn "Process did not stop gracefully, forcing termination..."\n\
            kill -KILL $PID 2>/dev/null || true\n\
        fi\n\
        \n\
        wait $PID 2>/dev/null || true\n\
    fi\n\
    log_success "Development server stopped"\n\
    exit 0\n\
}\n\
\n\
# Set up signal handlers\n\
trap cleanup TERM INT QUIT\n\
\n\
# Main execution with enhanced error handling and logging\n\
main() {\n\
    log_system_info\n\
    \n\
    # Ensure proper user context\n\
    ensure_proper_user\n\
    \n\
    log "================================================"\n\
    log "🚀 AlgoTrader Frontend Development Environment"\n\
    log "================================================"\n\
    \n\
    # Execute startup steps with comprehensive error handling\n\
    log "Step 1/5: Verifying configuration..."\n\
    if ! verify_config; then\n\
        log_error "Configuration verification failed"\n\
        exit 1\n\
    fi\n\
    \n\
    log "Step 2/5: Checking dependencies..."\n\
    if ! check_dependencies; then\n\
        log_error "Dependency check failed"\n\
        exit 1\n\
    fi\n\
    \n\
    log "Step 3/5: Setting up symlinks..."\n\
    if ! setup_symlinks; then\n\
        log_error "Symlink setup failed"\n\
        exit 1\n\
    fi\n\
    \n\
    log "Step 4/5: Clearing cache..."\n\
    if ! clear_cache; then\n\
        log_error "Cache clearing failed"\n\
        exit 1\n\
    fi\n\
    \n\
    log "Step 5/5: Final environment check..."\n\
    # Verify Next.js can be started\n\
    if ! command -v next >/dev/null 2>&1; then\n\
        log_error "Next.js CLI not found in PATH"\n\
        exit 1\n\
    fi\n\
    \n\
    log_success "All startup checks completed successfully"\n\
    log "================================================"\n\
    log "🌟 Starting Next.js development server with hot-reloading..."\n\
    log "📍 Access the application at: http://localhost:${PORT:-3000}"\n\
    log "🐛 Debug port available at: http://localhost:9229 (if debugging enabled)"\n\
    log "📁 Working Directory: $(pwd)"\n\
    log "👤 User: $(whoami)"\n\
    log "🔧 Node.js: $(node --version)"\n\
    log "📦 NPM: $(npm --version)"\n\
    log "🔥 Hot-reloading: ${FAST_REFRESH:-true}"\n\
    log "👀 File watching: ${WATCHPACK_POLLING:-true}"\n\
    log "================================================"\n\
    \n\
    # Start the development server with enhanced error handling\n\
    log "Starting Next.js development server..."\n\
    \n\
    # Use exec to replace the shell process for proper signal handling\n\
    exec npm run dev\n\
}\n\
\n\
# Execute main function\n\
main "$@"' > /usr/local/bin/start-dev.sh && \
    chmod +x /usr/local/bin/start-dev.sh

# Create enhanced debugging and development utilities script
RUN echo '#!/bin/bash\n\
# Development utilities and debugging tools for AlgoTrader Frontend\n\
set -e\n\
\n\
# Enhanced logging functions\n\
log() {\n\
    echo "[$(date "+%Y-%m-%d %H:%M:%S")] [INFO] $1"\n\
}\n\
\n\
log_warn() {\n\
    echo "[$(date "+%Y-%m-%d %H:%M:%S")] [WARN] $1"\n\
}\n\
\n\
log_error() {\n\
    echo "[$(date "+%Y-%m-%d %H:%M:%S")] [ERROR] $1"\n\
}\n\
\n\
log_success() {\n\
    echo "[$(date "+%Y-%m-%d %H:%M:%S")] [SUCCESS] $1"\n\
}\n\
\n\
# Enhanced volume detection function\n\
is_mounted_volume() {\n\
    local dir="$1"\n\
    if [ ! -d "$dir" ]; then\n\
        return 1\n\
    fi\n\
    \n\
    # Check multiple methods\n\
    if mountpoint -q "$dir" 2>/dev/null; then\n\
        return 0\n\
    fi\n\
    \n\
    if grep -q "/app/$dir" /proc/mounts 2>/dev/null; then\n\
        return 0\n\
    fi\n\
    \n\
    return 1\n\
}\n\
\n\
# Enhanced clean function\n\
clean_directory() {\n\
    local dir="$1"\n\
    local description="$2"\n\
    \n\
    if [ ! -d "$dir" ]; then\n\
        log "Directory $dir does not exist, skipping..."\n\
        return 0\n\
    fi\n\
    \n\
    if is_mounted_volume "$dir"; then\n\
        log "Detected $dir as mounted volume - clearing contents only..."\n\
        find "$dir" -mindepth 1 -maxdepth 1 -exec rm -rf {} + 2>/dev/null || {\n\
            log_warn "Some files in $dir could not be removed (normal for mounted volumes)"\n\
        }\n\
    else\n\
        log "Removing $dir directory completely..."\n\
        rm -rf "$dir" 2>/dev/null || {\n\
            log_warn "Could not remove $dir directory completely"\n\
        }\n\
    fi\n\
    \n\
    log_success "$description cleaned"\n\
}\n\
\n\
case "$1" in\n\
  "debug")\n\
    log "Starting development server in debug mode..."\n\
    log "Debug port: 9229"\n\
    log "Connect your IDE debugger to localhost:9229"\n\
    NODE_OPTIONS="--inspect=0.0.0.0:9229 --max-old-space-size=4096" npm run dev\n\
    ;;\n\
  "test")\n\
    log "Running tests..."\n\
    npm run test\n\
    ;;\n\
  "test:watch")\n\
    log "Running tests in watch mode..."\n\
    npm run test:watch\n\
    ;;\n\
  "test:coverage")\n\
    log "Running tests with coverage..."\n\
    npm run test:coverage\n\
    ;;\n\
  "test:unit")\n\
    log "Running unit tests..."\n\
    npm run test:unit\n\
    ;;\n\
  "test:integration")\n\
    log "Running integration tests..."\n\
    npm run test:integration\n\
    ;;\n\
  "test:e2e")\n\
    log "Running end-to-end tests..."\n\
    npm run test:e2e\n\
    ;;\n\
  "lint")\n\
    log "Running linter..."\n\
    npm run lint\n\
    ;;\n\
  "lint:fix")\n\
    log "Running linter with auto-fix..."\n\
    npm run lint -- --fix\n\
    ;;\n\
  "type-check")\n\
    log "Running TypeScript type check..."\n\
    npm run type-check\n\
    ;;\n\
  "build")\n\
    log "Building application..."\n\
    npm run build\n\
    ;;\n\
  "build:analyze")\n\
    log "Building application with bundle analysis..."\n\
    ANALYZE=true npm run build\n\
    ;;\n\
  "storybook")\n\
    log "Starting Storybook..."\n\
    npm run storybook\n\
    ;;\n\
  "clean")\n\
    log "Cleaning build artifacts..."\n\
    clean_directory ".next" "Next.js build cache"\n\
    clean_directory "coverage" "Test coverage reports"\n\
    clean_directory "test-results" "Test results"\n\
    rm -rf node_modules/.cache 2>/dev/null || true\n\
    log_success "Build artifacts cleaned"\n\
    ;;\n\
  "clean:all")\n\
    log "Cleaning all artifacts including node_modules..."\n\
    clean_directory ".next" "Next.js build cache"\n\
    clean_directory "node_modules" "Node.js dependencies"\n\
    clean_directory "coverage" "Test coverage reports"\n\
    clean_directory "test-results" "Test results"\n\
    log_success "All artifacts cleaned"\n\
    ;;\n\
  "install")\n\
    log "Installing dependencies..."\n\
    npm install --legacy-peer-deps --no-audit --no-fund\n\
    ;;\n\
  "install:clean")\n\
    log "Clean installing dependencies..."\n\
    clean_directory "node_modules" "Node.js dependencies"\n\
    rm -f package-lock.json 2>/dev/null || true\n\
    npm install --legacy-peer-deps --no-audit --no-fund\n\
    ;;\n\
  "update")\n\
    log "Updating dependencies..."\n\
    npm update --legacy-peer-deps\n\
    ;;\n\
  "audit")\n\
    log "Running security audit..."\n\
    npm audit\n\
    ;;\n\
  "audit:fix")\n\
    log "Running security audit with auto-fix..."\n\
    npm audit fix --legacy-peer-deps\n\
    ;;\n\
  "health")\n\
    log "=== Health Check ==="\n\
    echo "Node.js: $(node --version)"\n\
    echo "NPM: $(npm --version)"\n\
    echo "Next.js: $(npx next --version 2>/dev/null || echo '"'"'Not available'"'"')"\n\
    echo "TypeScript: $(npx tsc --version 2>/dev/null || echo '"'"'Not available'"'"')"\n\
    echo "Working Directory: $(pwd)"\n\
    echo "User: $(whoami)"\n\
    echo "Environment: ${NODE_ENV:-development}"\n\
    echo "Port: ${PORT:-3000}"\n\
    echo "Memory Limit: ${NODE_OPTIONS:-default}"\n\
    echo "Hot-reloading: ${FAST_REFRESH:-true}"\n\
    echo "File watching: ${WATCHPACK_POLLING:-true}"\n\
    \n\
    # Check if dependencies are installed\n\
    if [ -d "node_modules" ] && [ -f "node_modules/.package-lock.json" ]; then\n\
        echo "Dependencies: ✓ Installed"\n\
    else\n\
        echo "Dependencies: ✗ Not installed or incomplete"\n\
    fi\n\
    \n\
    # Check configuration files\n\
    [ -f "package.json" ] && echo "package.json: ✓" || echo "package.json: ✗"\n\
    [ -f "next.config.js" ] && echo "next.config.js: ✓" || echo "next.config.js: ✗"\n\
    [ -f "tsconfig.json" ] && echo "tsconfig.json: ✓" || echo "tsconfig.json: ✗"\n\
    [ -f "tailwind.config.js" ] && echo "tailwind.config.js: ✓" || echo "tailwind.config.js: ✗"\n\
    \n\
    # Check if project can be built\n\
    if command -v next >/dev/null 2>&1; then\n\
        echo "Next.js CLI: ✓ Available"\n\
    else\n\
        echo "Next.js CLI: ✗ Not available"\n\
    fi\n\
    ;;\n\
  "info")\n\
    log "=== Development Environment Info ==="\n\
    echo "Node.js: $(node --version)"\n\
    echo "NPM: $(npm --version)"\n\
    echo "Working Directory: $(pwd)"\n\
    echo "User: $(whoami)"\n\
    echo "Environment: ${NODE_ENV:-development}"\n\
    echo "Port: ${PORT:-3000}"\n\
    echo "Memory Limit: ${NODE_OPTIONS:-default}"\n\
    if [ -f "package.json" ]; then\n\
        echo "Project: $(cat package.json | grep '"'"'"name"'"'"' | cut -d'"'"'":"'"'"' -f2 | tr -d '"'"' "'"'"')"\n\
        echo "Version: $(cat package.json | grep '"'"'"version"'"'"' | cut -d'"'"'":"'"'"' -f2 | tr -d '"'"' "'"'"')"\n\
    fi\n\
    ;;\n\
  "help"|*)\n\
    echo "=== AlgoTrader Frontend Development Tools ==="\n\
    echo "Available commands:"\n\
    echo "  debug         - Start with debugging enabled (port 9229)"\n\
    echo "  test          - Run tests"\n\
    echo "  test:watch    - Run tests in watch mode"\n\
    echo "  test:coverage - Run tests with coverage report"\n\
    echo "  test:unit     - Run unit tests only"\n\
    echo "  test:integration - Run integration tests only"\n\
    echo "  test:e2e      - Run end-to-end tests"\n\
    echo "  lint          - Run linter"\n\
    echo "  lint:fix      - Run linter with auto-fix"\n\
    echo "  type-check    - Run TypeScript type checking"\n\
    echo "  build         - Build the application"\n\
    echo "  build:analyze - Build with bundle analysis"\n\
    echo "  storybook     - Start Storybook"\n\
    echo "  clean         - Clean build artifacts"\n\
    echo "  clean:all     - Clean all artifacts including node_modules"\n\
    echo "  install       - Install dependencies"\n\
    echo "  install:clean - Clean install dependencies"\n\
    echo "  update        - Update dependencies"\n\
    echo "  audit         - Run security audit"\n\
    echo "  audit:fix     - Run security audit with auto-fix"\n\
    echo "  health        - Show comprehensive health check"\n\
    echo "  info          - Show environment information"\n\
    echo "  help          - Show this help"\n\
    echo "============================================"\n\
    ;;\n\
esac' > /usr/local/bin/dev-tools.sh && \
    chmod +x /usr/local/bin/dev-tools.sh

# Set comprehensive environment variables for development
ENV NODE_ENV=development
ENV PORT=3000
ENV HOSTNAME="0.0.0.0"
ENV NEXT_TELEMETRY_DISABLED=1
ENV NEXT_PUBLIC_ENABLE_SW_IN_DEV=false

# File watching optimizations for hot-reloading
ENV WATCHPACK_POLLING=true
ENV CHOKIDAR_USEPOLLING=true
ENV CHOKIDAR_INTERVAL=1000
ENV FAST_REFRESH=true

# Development-specific optimizations
ENV NODE_OPTIONS="--max-old-space-size=4096"

# Performance and debugging
ENV FORCE_COLOR=1
ENV NPM_CONFIG_COLOR=always

# Create log directory and set permissions
RUN mkdir -p /app/logs && chown -R nextjs:nodejs /app/logs

# Expose ports for development server and debugging
EXPOSE 3000 9229

# Add comprehensive health check for development environment
HEALTHCHECK --interval=30s --timeout=15s --start-period=90s --retries=5 \
    CMD ["/usr/local/bin/healthcheck.sh"]

# Switch to non-root user for security
USER nextjs

# Use development startup script as default command
CMD ["/usr/local/bin/start-dev.sh"]

# ================================================================================================
# Stage 6: Testing Environment - Comprehensive testing setup
# ================================================================================================
FROM base AS testing

# Switch back to root for installing test dependencies
USER root

# Copy dependencies from dependencies stage
COPY --from=dependencies --chown=nextjs:nodejs /app/node_modules ./node_modules
COPY --from=dependencies --chown=nextjs:nodejs /app/package*.json ./

# Install additional testing tools and dependencies
RUN npm install --no-save --legacy-peer-deps \
    @playwright/test \
    jest-environment-jsdom \
    @testing-library/jest-dom \
    @testing-library/react \
    @testing-library/user-event \
    jest-axe \
    lighthouse \
    puppeteer

# Install Playwright browsers
RUN npx playwright install --with-deps

# Create comprehensive test runner script
RUN echo '#!/bin/bash\n\
# Comprehensive test runner for AlgoTrader Frontend\n\
set -e\n\
\n\
# Logging function\n\
log() {\n\
    echo "[$(date "+%Y-%m-%d %H:%M:%S")] $1"\n\
}\n\
\n\
case "$1" in\n\
  "unit")\n\
    log "Running unit tests..."\n\
    npm run test:unit\n\
    ;;\n\
  "integration")\n\
    log "Running integration tests..."\n\
    npm run test:integration\n\
    ;;\n\
  "e2e")\n\
    log "Running end-to-end tests..."\n\
    npm run test:e2e\n\
    ;;\n\
  "accessibility")\n\
    log "Running accessibility tests..."\n\
    npm run test:accessibility\n\
    ;;\n\
  "coverage")\n\
    log "Running tests with coverage..."\n\
    npm run test:coverage\n\
    ;;\n\
  "all")\n\
    log "Running all tests..."\n\
    npm run test:all\n\
    ;;\n\
  "ci")\n\
    log "Running CI tests..."\n\
    npm run test:ci\n\
    ;;\n\
  *)\n\
    log "Running default tests..."\n\
    npm run test\n\
    ;;\n\
esac' > /usr/local/bin/test-runner.sh && \
    chmod +x /usr/local/bin/test-runner.sh

# Set testing environment variables
ENV NODE_ENV=test
ENV CI=true

# Switch back to non-root user
USER nextjs

# Default command for testing stage
CMD ["/usr/local/bin/test-runner.sh"]

# ================================================================================================
# Stage 7: Production Stage - Optimized for security, performance, and minimal size
# ================================================================================================
FROM node:22.15.0-alpine AS production

# Install minimal runtime dependencies
RUN apk add --no-cache \
    libc6-compat \
    curl \
    ca-certificates \
    dumb-init \
    tini \
    && rm -rf /var/cache/apk/*

# Set working directory
WORKDIR /app

# Create non-root user for security
RUN addgroup -g 1001 -S nodejs && \
    adduser -S nextjs -u 1001 -G nodejs

# Copy built application from builder stage
COPY --from=builder --chown=nextjs:nodejs /app/.next/standalone ./
COPY --from=builder --chown=nextjs:nodejs /app/.next/static ./.next/static
COPY --from=builder --chown=nextjs:nodejs /app/public ./public

# Create production health check script
RUN echo '#!/bin/sh\n\
# Production health check\n\
curl -f -s --max-time 10 http://localhost:${PORT:-3000}/api/health >/dev/null 2>&1 || \\\n\
curl -f -s --max-time 10 http://localhost:${PORT:-3000} >/dev/null 2>&1' > /usr/local/bin/healthcheck.sh && \
    chmod +x /usr/local/bin/healthcheck.sh

# Set production environment variables
ENV NODE_ENV=production
ENV PORT=3000
ENV HOSTNAME="0.0.0.0"
ENV NEXT_TELEMETRY_DISABLED=1

# Expose port
EXPOSE 3000

# Add health check
HEALTHCHECK --interval=30s --timeout=10s --start-period=30s --retries=3 \
    CMD ["/usr/local/bin/healthcheck.sh"]

# Switch to non-root user
USER nextjs

# Start the application
CMD ["dumb-init", "node", "server.js"]
