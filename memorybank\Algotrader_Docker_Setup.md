# AlgoTrader Docker Setup Documentation

## Table of Contents

1. [Introduction](#introduction)
2. [Architecture Overview](#architecture-overview)
   - [Container Architecture](#container-architecture)
   - [Data Flow Diagram](#data-flow-diagram)
   - [Connection Diagram](#connection-diagram)
3. [Container Details](#container-details)
   - [Backend API (algotrader-backend)](#backend-api-algotrader-backend)
   - [Worker (algotrader-worker)](#worker-algotrader-worker)
   - [Worker Beat (algotrader-worker-beat)](#worker-beat-algotrader-worker-beat)
   - [Frontend (algotrader-frontend)](#frontend-algotrader-frontend)
   - [Frontend Development (algotrader-frontend-dev)](#frontend-development-algotrader-frontend-dev)
   - [PostgreSQL (algotrader-postgres)](#postgresql-algotrader-postgres)
   - [TimescaleDB (algotrader-timescaledb)](#timescaledb-algotrader-timescaledb)
   - [Redis (algotrader-redis)](#redis-algotrader-redis)
   - [PgAdmin (algotrader-pgadmin)](#pgadmin-algotrader-pgadmin)
   - [Flower (algotrader-flower)](#flower-algotrader-flower)
   - [Storybook (algotrader-storybook)](#storybook-algotrader-storybook)
4. [Setup Instructions](#setup-instructions)
   - [Prerequisites](#prerequisites)
   - [Development Environment Setup](#development-environment-setup)
   - [Production Environment Setup](#production-environment-setup)
5. [Configuration](#configuration)
   - [Environment Variables](#environment-variables)
   - [Docker Compose Overrides](#docker-compose-overrides)
6. [Workflow Diagrams](#workflow-diagrams)
   - [Development Workflow](#development-workflow)
   - [Deployment Workflow](#deployment-workflow)
   - [Data Processing Workflow](#data-processing-workflow)
7. [Troubleshooting](#troubleshooting)
   - [Common Issues](#common-issues)
   - [Worker and Worker-Beat Issues](#worker-and-worker-beat-issues)
   - [Database Connection Issues](#database-connection-issues)
8. [Maintenance](#maintenance)
   - [Updating Containers](#updating-containers)
   - [Backing Up Data](#backing-up-data)
   - [Monitoring](#monitoring)

## Introduction

AlgoTrader is a comprehensive algorithmic trading platform that uses Docker containers for deployment. This document provides detailed information about the Docker setup, including architecture diagrams, container details, setup instructions, and troubleshooting guides.

The application is designed to be modular, scalable, and easy to deploy in both development and production environments. Docker containers are used to encapsulate each component of the application, ensuring consistency across different environments and simplifying the deployment process.

## Architecture Overview

### Container Architecture

```
┌─────────────────────────────────────────────────────────────────────┐
│                        AlgoTrader Architecture                       │
└─────────────────────────────────────────────────────────────────────┘
                                   │
                                   ▼
┌─────────────────────────────────────────────────────────────────────┐
│                           Frontend Layer                             │
├───────────────┬───────────────────────────────┬───────────────────┬─┘
│ Frontend      │ Frontend Development          │ Storybook         │
│ (Next.js)     │ (Next.js with Hot Reloading)  │ (Component Dev)   │
└───────┬───────┴───────────────┬───────────────┴───────────────────┘
        │                       │
        ▼                       ▼
┌───────────────────────────────────────────────────────────────────┐
│                           Backend Layer                            │
├───────────────┬───────────────────────────────┬───────────────────┤
│ Backend API   │ Worker                        │ Worker Beat       │
│ (FastAPI)     │ (Celery Worker)               │ (Celery Beat)     │
└───────┬───────┴───────────────┬───────────────┴────────┬──────────┘
        │                       │                        │
        ▼                       ▼                        ▼
┌───────────────────────────────────────────────────────────────────┐
│                          Monitoring Layer                          │
├───────────────────────────────────────────────┬───────────────────┤
│ Flower                                        │ PgAdmin           │
│ (Celery Monitoring)                           │ (DB Admin)        │
└───────────────────────────────────────────────┴───────────────────┘
        │                       │                        │
        ▼                       ▼                        ▼
┌───────────────────────────────────────────────────────────────────┐
│                           Data Layer                               │
├───────────────┬───────────────────────────────┬───────────────────┤
│ PostgreSQL    │ TimescaleDB                   │ Redis             │
│ (User Data)   │ (Time-Series Data)            │ (Cache/Broker)    │
└───────────────┴───────────────────────────────┴───────────────────┘
```

### Data Flow Diagram

```
┌──────────────┐     HTTP     ┌──────────────┐     SQL      ┌──────────────┐
│   Frontend   │─────────────▶│  Backend API │─────────────▶│  PostgreSQL  │
└──────────────┘              └──────────────┘              └──────────────┘
       │                             │                             ▲
       │                             │                             │
       │                             ▼                             │
       │                      ┌──────────────┐                     │
       │                      │    Redis     │                     │
       │                      └──────────────┘                     │
       │                             │                             │
       │                             ▼                             │
       │                      ┌──────────────┐     SQL      ┌──────────────┐
       │                      │    Worker    │─────────────▶│  TimescaleDB │
       │                      └──────────────┘              └──────────────┘
       │                             ▲                             ▲
       │                             │                             │
       │                             │                             │
       │                      ┌──────────────┐                     │
       └─────────────────────▶│  Worker Beat │─────────────────────┘
                              └──────────────┘
```

### Connection Diagram

```
┌──────────────┐
│   Frontend   │──┐
└──────────────┘  │
                  │ HTTP/WebSocket
┌──────────────┐  │    ┌──────────────┐
│ Frontend Dev │──┼───▶│  Backend API │
└──────────────┘  │    └──────────────┘
                  │           │
┌──────────────┐  │           │ SQL
│   Storybook  │──┘           ▼
└──────────────┘        ┌──────────────┐
                        │  PostgreSQL  │◀─┐
                        └──────────────┘  │
                                          │ SQL Admin
┌──────────────┐                          │
│    Flower    │                  ┌──────────────┐
└──────────────┘                  │   PgAdmin    │
       ▲                          └──────────────┘
       │ HTTP                             │
       │                                  │
┌──────────────┐                          │
│    Worker    │◀─────────────────────────┘
└──────────────┘
       ▲
       │ Redis
       │
┌──────────────┐        ┌──────────────┐
│  Worker Beat │◀───────│    Redis     │
└──────────────┘        └──────────────┘
       │                        ▲
       │                        │
       │                        │
       └────────────────────────┘
```

## Container Details

### Backend API (algotrader-backend)

**Function**: FastAPI application serving the REST API, handling authentication, data access, and business logic.

**Dockerfile**: `Dockerfile`

**Base Image**: `continuumio/miniconda3:23.10.0-1`

**Key Features**:
- RESTful API with OpenAPI documentation
- JWT authentication
- Database ORM with SQLAlchemy
- Async request handling
- WebSocket support for real-time data
- Celery task integration

**Dependencies**:
- postgres
- timescaledb
- redis

### Worker (algotrader-worker)

**Function**: Celery worker for processing background tasks such as data collection, order management, and reporting.

**Dockerfile**: `Dockerfile.worker`

**Base Image**: `continuumio/miniconda3:23.10.0-1`

**Key Features**:
- Asynchronous task processing
- Task prioritization
- Task retries and error handling
- Task result storage
- Task routing to specific queues

**Dependencies**:
- postgres
- timescaledb
- redis

### Worker Beat (algotrader-worker-beat)

**Function**: Celery beat for scheduling periodic tasks such as market data updates and maintenance operations.

**Dockerfile**: `Dockerfile.worker-beat`

**Base Image**: `continuumio/miniconda3:23.10.0-1`

**Key Features**:
- Periodic task scheduling
- Crontab-like scheduling
- Dynamic schedule updates
- Integration with Celery workers
- Market-aware scheduling

**Dependencies**:
- worker
- redis

### Frontend (algotrader-frontend)

**Function**: Next.js application serving the web interface for the AlgoTrader application.

**Dockerfile**: `frontend/Dockerfile`

**Base Image**: `node:22.15.0-alpine`

**Key Features**:
- Server-side rendering with Next.js
- React components
- Responsive design
- PWA capabilities
- Authentication integration with backend

**Dependencies**:
- backend

### Frontend Development (algotrader-frontend-dev)

**Function**: Production-level development environment with comprehensive multi-stage build approach for Next.js frontend development.

**Dockerfile**: `frontend/Dockerfile.dev`

**Base Image**: `node:22.15.0-alpine`

**Architecture**: Multi-stage Docker build with optimized stages:
1. **Base Stage**: Common foundation with all system dependencies
2. **Dependencies Stage**: Node.js dependencies installation and caching
3. **Development Stage**: Development tools and debugging capabilities
4. **Runtime Stage**: Optimized development runtime environment

**Key Features**:
- **Enhanced Hot-reloading** with optimized file watching (WATCHPACK_POLLING, CHOKIDAR_USEPOLLING)
- **React Fast Refresh** with performance optimizations and error recovery
- **Advanced Debugging** with Node.js Inspector Protocol on port 9229
- **Comprehensive Development Tools** via `/usr/local/bin/dev-tools.sh` (test, lint, build, debug, health)
- **Intelligent Volume Management** with automatic detection of mounted volumes
- **Production-level Logging** with structured logging and error handling
- **Security-first Approach** with non-root user execution and proper permissions
- **Cross-platform Compatibility** optimized for Windows, macOS, and Linux development
- **Resource Optimization** with memory limits and intelligent caching strategies

**Technical Specifications**:
- **Memory Management**: 2GB limit with 1GB reservation for optimal performance
- **Volume Strategy**:
  - Cached source code mounting for hot-reloading
  - Persistent node_modules volume for dependency isolation
  - Intelligent .next cache management with volume detection
  - Separate volumes for logs, coverage, and test results
- **Environment Optimizations**:
  - WATCHPACK_POLLING=true (cross-platform file watching)
  - CHOKIDAR_USEPOLLING=true with 1000ms interval
  - FAST_REFRESH=true (React Fast Refresh)
  - NODE_OPTIONS=--max-old-space-size=4096 (memory optimization)
  - NEXT_TELEMETRY_DISABLED=1 (privacy and performance)
- **Development Scripts**:
  - `/usr/local/bin/start-dev.sh`: Enhanced startup with comprehensive validation
  - `/usr/local/bin/dev-tools.sh`: Complete development toolkit
  - `/usr/local/bin/healthcheck.sh`: Advanced health monitoring
- **Port Exposure**:
  - 3000: Next.js development server
  - 9229: Node.js debugging (Inspector Protocol)

**Development Tools Available**:
- **Testing**: Unit, integration, e2e, coverage reporting
- **Code Quality**: ESLint, Prettier, TypeScript checking
- **Building**: Development and production builds with analysis
- **Debugging**: Integrated debugger support with IDE connectivity
- **Utilities**: Clean, install, update, audit, health checks

**Dependencies**:
- backend (for API connectivity)

**Management**:
- Use `scripts/build-frontend-dev.sh` for comprehensive container building and testing
- Automated health checks and startup validation
- Comprehensive error handling and recovery mechanisms

### PostgreSQL (algotrader-postgres)

**Function**: Primary relational database for storing user data, strategies, and configuration.

**Image**: `postgres:15-alpine`

**Key Features**:
- ACID-compliant relational database
- Transactional support
- JSON and JSONB data types
- Full-text search
- Extensible with extensions

### TimescaleDB (algotrader-timescaledb)

**Function**: Time-series database for storing market data and time-series analytics.

**Image**: `timescale/timescaledb:latest-pg15`

**Key Features**:
- Time-series optimized database
- Hypertables for efficient time-series storage
- Continuous aggregates for real-time materialized views
- Advanced time-series functions
- Compatible with PostgreSQL

### Redis (algotrader-redis)

**Function**: Cache and message broker for Celery tasks and application caching.

**Image**: `redis:7-alpine`

**Key Features**:
- In-memory data store
- Pub/sub messaging
- Lua scripting
- Transactions
- Persistence with AOF

### PgAdmin (algotrader-pgadmin)

**Function**: Web-based PostgreSQL administration tool for managing and exploring the databases.

**Dockerfile**: `Dockerfile.pgadmin`

**Base Image**: `dpage/pgadmin4:latest`

**Key Features**:
- Web-based database administration
- Query tool
- Server management
- Database object explorer
- SQL editor

**Dependencies**:
- postgres
- timescaledb

### Flower (algotrader-flower)

**Function**: Web-based Celery monitoring tool for monitoring and managing Celery tasks.

**Dockerfile**: `Dockerfile.flower`

**Base Image**: `continuumio/miniconda3:23.10.0-1`

**Key Features**:
- Real-time monitoring of Celery tasks
- Task history and statistics
- Worker monitoring
- Task management (revoke, terminate)
- Authentication support

**Dependencies**:
- worker
- redis

### Storybook (algotrader-storybook)

**Function**: Component development and documentation tool for frontend components.

**Dockerfile**: `frontend/Dockerfile.storybook`

**Base Image**: `node:22.15.0-alpine`

**Key Features**:
- Component isolation
- Interactive testing
- Documentation
- Accessibility testing
- Visual regression testing support

## Setup Instructions

### Prerequisites

Before you begin, ensure you have the following installed:

- Docker Desktop (Windows/Mac) or Docker Engine (Linux) version 24.0+
- Docker Compose version 2.0+
- Git
- PowerShell (Windows) or Bash (Linux/Mac)

### Development Environment Setup

1. Clone the repository and navigate to the project directory:
   ```bash
   git clone https://github.com/yourusername/algotrader.git
   cd algotrader
   ```

2. Create a `.env` file from the example:
   ```bash
   cp .env.example .env
   ```

3. Start the development environment:
   ```bash
   docker-compose up -d
   ```

4. Access the services:
   - Backend API: http://localhost:8000/docs
   - Frontend (Development): http://localhost:3000
   - Storybook: http://localhost:6006
   - Flower (Celery Monitoring): http://localhost:5555
   - PgAdmin: http://localhost:5050 (Email: <EMAIL>, Password: admin)

### Production Environment Setup

To run in production mode:

```bash
docker-compose -f docker-compose.yml -f docker-compose.prod.yml up -d
```

The production environment includes:
- Optimized builds for performance
- Multi-worker configuration
- Production-specific configurations
- Health checks and automatic restarts

## Configuration

### Environment Variables

The AlgoTrader application uses environment variables for configuration. These can be set in the `.env` file or passed directly to the containers.

Key environment variables include:

- `ENVIRONMENT`: `development` or `production`
- `DATABASE_URL`: PostgreSQL connection string
- `TIMESCALE_URL`: TimescaleDB connection string
- `REDIS_URL`: Redis connection string
- `API_DEBUG`: Enable/disable debug mode
- `LOG_LEVEL`: Logging level
- `API_CORS_ORIGINS`: Allowed CORS origins

See the `.env.example` file for a complete list of available environment variables and their default values.

### Docker Compose Overrides

You can create a `docker-compose.override.yml` file to override specific settings in the Docker Compose file. This is useful for local development or testing.

Example override for local development:

```yaml
version: '3.8'

services:
  backend:
    ports:
      - "8000:8000"
      - "5678:5678"  # For debugger
    environment:
      - API_DEBUG=true
      - LOG_LEVEL=DEBUG

  frontend-dev:
    ports:
      - "3000:3000"
    environment:
      - NODE_ENV=development
      - NEXT_PUBLIC_API_URL=http://localhost:8000/api
```

## Workflow Diagrams

### Development Workflow

```
┌──────────────┐     ┌──────────────┐     ┌──────────────┐
│  Local Code  │────▶│  Git Commit  │────▶│  Git Push    │
└──────────────┘     └──────────────┘     └──────────────┘
                                                  │
                                                  ▼
┌──────────────┐     ┌──────────────┐     ┌──────────────┐
│  Hot Reload  │◀───▶│  Docker Dev  │◀────│  Git Pull    │
└──────────────┘     └──────────────┘     └──────────────┘
       │                     │
       ▼                     ▼
┌──────────────┐     ┌──────────────┐
│  Browser     │     │  API Testing │
└──────────────┘     └──────────────┘
```

The development workflow involves:
1. Making code changes locally
2. Testing changes with hot-reloading in the development environment
3. Committing and pushing changes to the repository
4. Pulling changes on other development machines
5. Testing API endpoints and frontend components

### Deployment Workflow

```
┌──────────────┐     ┌──────────────┐     ┌──────────────┐
│  Git Push    │────▶│  CI Pipeline │────▶│  Build Image │
└──────────────┘     └──────────────┘     └──────────────┘
                                                  │
                                                  ▼
┌──────────────┐     ┌──────────────┐     ┌──────────────┐
│  Monitoring  │◀───▶│  Production  │◀────│  Deploy      │
└──────────────┘     └──────────────┘     └──────────────┘
```

The deployment workflow involves:
1. Pushing code changes to the repository
2. Running CI/CD pipeline tests
3. Building Docker images
4. Deploying to production environment
5. Monitoring the application

### Data Processing Workflow

```
┌──────────────┐     ┌──────────────┐     ┌──────────────┐
│  Market Data │────▶│  Worker Beat │────▶│  Task Queue  │
└──────────────┘     └──────────────┘     └──────────────┘
                                                  │
                                                  ▼
┌──────────────┐     ┌──────────────┐     ┌──────────────┐
│  Frontend    │◀───▶│  Backend API │◀────│  Worker      │
└──────────────┘     └──────────────┘     └──────────────┘
       │                     │                    │
       │                     ▼                    ▼
       │              ┌──────────────┐    ┌──────────────┐
       └─────────────▶│  PostgreSQL  │◀───│ TimescaleDB  │
                      └──────────────┘    └──────────────┘
```

The data processing workflow involves:
1. Scheduling data collection tasks via Worker Beat
2. Adding tasks to the Redis queue
3. Processing tasks with Worker
4. Storing results in TimescaleDB
5. Making processed data available via the Backend API
6. Displaying data in the Frontend

## Troubleshooting

### Common Issues

1. **Port conflicts**: If you have services running on the same ports, change the port mappings in docker-compose.yml

2. **Database connection issues**: Ensure the database containers are healthy:
   ```bash
   docker-compose ps
   ```

3. **Worker not processing tasks**: Check the worker logs:
   ```bash
   docker-compose logs worker
   ```

4. **Frontend build errors**: Check the frontend logs:
   ```bash
   docker-compose logs frontend-dev
   ```

5. **Circular import errors**: The fixed Dockerfiles include patches for circular imports in the database models. If you encounter similar issues, check the import structure in your Python modules.

### Worker and Worker-Beat Issues

#### Scheduler Syntax Error

A common issue with the worker and worker-beat containers is a syntax error in the scheduler module:

```
File "/app/algotrader/tasks/scheduler/scheduler.py", line 182
    elif self.schedule_type == ScheduleType.SOLAR:
    ^^^^
SyntaxError: invalid syntax
```

This error occurs due to an indentation issue in the `scheduler.py` file.

**Solution**:

Create a fix script (`fix_scheduler.sh`) to correct the indentation:

```bash
#!/bin/bash
# Fix for the scheduler.py file

set -e

# Path to the scheduler.py file
SCHEDULER_PATH="/app/algotrader/tasks/scheduler/scheduler.py"

# Create a backup of the original file
cp $SCHEDULER_PATH ${SCHEDULER_PATH}.bak

# Fix the indentation issue with SOLAR schedule type
sed -i 's/    elif self.schedule_type == ScheduleType.SOLAR:/elif self.schedule_type == ScheduleType.SOLAR:/g' $SCHEDULER_PATH
```

#### Worker-Beat Not Connecting to Worker

The worker-beat container may show warnings about not being able to connect to the worker container:

```
Waiting for worker to be available...
Attempt 30/30: Worker is unavailable - waiting 5s
WARNING: Worker may not be available, but continuing anyway...
```

**Solution**:

This warning doesn't affect the functionality of the worker-beat container. It will still schedule tasks correctly, and the worker will process them. If you want to eliminate the warning, ensure the worker container is started before the worker-beat container by adjusting the `depends_on` configuration in the docker-compose file.

### Database Connection Issues

If the worker or worker-beat containers cannot connect to the database, check the following:

1. Ensure the database containers are running:
   ```bash
   docker-compose ps postgres timescaledb
   ```

2. Verify the database connection strings in the environment variables:
   ```bash
   docker-compose exec worker env | grep DATABASE_URL
   docker-compose exec worker env | grep TIMESCALE_URL
   ```

3. Check if the database is accessible from the worker container:
   ```bash
   docker-compose exec worker conda run -n algotrader python -c "import psycopg2; psycopg2.connect('$DATABASE_URL')"
   ```

4. Ensure the database initialization scripts have run successfully:
   ```bash
   docker-compose logs postgres | grep "database system is ready to accept connections"
   ```

## Maintenance

### Updating Containers

To update the containers with the latest code changes:

```powershell
# Pull the latest code
git pull

# Rebuild and restart the containers
docker-compose up -d --build
```

For a more comprehensive rebuild:

```powershell
# Stop all containers
docker-compose down

# Remove all images
docker-compose down --rmi all

# Rebuild and restart
docker-compose up -d --build
```

### Backing Up Data

#### Database Backup

To back up the database data:

```powershell
# Backup PostgreSQL data
docker exec algotrader-postgres pg_dump -U postgres algotrader > backup_postgres.sql

# Backup TimescaleDB data
docker exec algotrader-timescaledb pg_dump -U postgres algotrader_timeseries > backup_timescale.sql
```

#### Volume Backup

To back up Docker volumes:

```powershell
# Create a backup directory
mkdir -p backups

# Backup PostgreSQL volume
docker run --rm -v algotrader_postgres_data:/source -v $(pwd)/backups:/backup alpine tar -czf /backup/postgres_data.tar.gz -C /source .

# Backup TimescaleDB volume
docker run --rm -v algotrader_timescaledb_data:/source -v $(pwd)/backups:/backup alpine tar -czf /backup/timescaledb_data.tar.gz -C /source .

# Backup Redis volume
docker run --rm -v algotrader_redis_data:/source -v $(pwd)/backups:/backup alpine tar -czf /backup/redis_data.tar.gz -C /source .
```

### Monitoring

#### Container Health

Monitor container health using Docker's built-in health checks:

```powershell
docker ps --format "table {{.Names}}\t{{.Status}}\t{{.Ports}}"
```

#### Resource Usage

Monitor resource usage with Docker stats:

```powershell
docker stats $(docker ps --format "{{.Names}}")
```

#### Logs

Monitor logs in real-time:

```powershell
# All containers
docker-compose logs -f

# Specific container
docker-compose logs -f worker
```

#### Celery Tasks

Monitor Celery tasks using Flower:

1. Access the Flower dashboard at http://localhost:5555
2. View active tasks, task history, and worker status
3. Monitor task execution times and success/failure rates

## Integration Diagram

```
┌───────────────────────────────────────────────────────────────────┐
│                      External Systems                              │
├───────────────┬───────────────────────────────┬───────────────────┤
│ Market Data   │ Broker APIs                   │ News Services     │
│ Providers     │                               │                   │
└───────┬───────┴───────────────┬───────────────┴────────┬──────────┘
        │                       │                        │
        ▼                       ▼                        ▼
┌───────────────────────────────────────────────────────────────────┐
│                      AlgoTrader Backend                            │
├───────────────┬───────────────────────────────┬───────────────────┤
│ Data          │ Strategy                      │ Order             │
│ Collection    │ Execution                     │ Management        │
└───────┬───────┴───────────────┬───────────────┴────────┬──────────┘
        │                       │                        │
        ▼                       ▼                        ▼
┌───────────────────────────────────────────────────────────────────┐
│                      AlgoTrader Frontend                           │
├───────────────┬───────────────────────────────┬───────────────────┤
│ Dashboards    │ Strategy Builder              │ Backtesting       │
│               │                               │                   │
└───────────────┴───────────────────────────────┴───────────────────┘
```

This integration diagram shows how AlgoTrader connects with external systems and how data flows between different components of the application.

## Conclusion

This documentation provides a comprehensive guide to the Docker setup for the AlgoTrader application. By following the instructions and diagrams provided, you should be able to set up, configure, and maintain the application in both development and production environments.

For additional support or questions, please refer to the project's GitHub repository or contact the development team.
